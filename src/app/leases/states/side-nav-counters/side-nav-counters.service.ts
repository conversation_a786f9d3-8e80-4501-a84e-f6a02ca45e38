import { Injectable } from '@angular/core';
import { SideNavCountersStore } from './side-nav-counters.store';
import { BookmarksApi } from '../../api';
import { TeamworkApi } from '../../modules/teamwork/api/teamwork.api';

@Injectable()
export class SideNavCountersService {

    constructor(
        private readonly sideNavCountersStore: SideNavCountersStore,
        private readonly bookmarksApi: BookmarksApi,
        private readonly teamworkApi: TeamworkApi,
    ) {
    }

    public load(): void {
        this.bookmarksApi.getActive()
            .subscribe((statistic) => {
                this.sideNavCountersStore.update({
                    previousActiveProjects: statistic.active
                });
            });
        this.teamworkApi.getActiveProjects()
            .subscribe((statistic) => {
                this.sideNavCountersStore.update({
                    teamProjects: statistic.active
                });
            });
    }
}
