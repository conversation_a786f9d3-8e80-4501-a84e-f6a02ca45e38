import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TeamworkApi } from './api/teamwork.api';
import { TeamProjectsService } from './stores/team-projects.service';
import { TeamProjectsStore } from './stores/team-projects.store';
import { TeamProjectsPageComponent } from './components/team-projects-page/team-projects-page.component';
import { TeamProjectsTableComponent } from './components/team-projects-table/team-projects-table.component';
import { SharedModule } from '@shared/shared.module';
import { TeamProjectsQuery } from './stores/team-projects.query';
import { MatMenuModule } from '@angular/material/menu';


@NgModule({
    declarations: [
        TeamProjectsPageComponent,
        TeamProjectsTableComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        MatMenuModule,
    ],
    providers: [
        TeamworkApi,
        TeamProjectsService,
        TeamProjectsStore,
        TeamProjectsQuery,
    ],
})
export class TeamworkModule {
}
