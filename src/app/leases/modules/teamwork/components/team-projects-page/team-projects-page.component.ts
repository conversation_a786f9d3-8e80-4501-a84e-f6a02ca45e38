import { Component, OnDestroy } from '@angular/core';
import { TeamProjectsService } from '../../stores/team-projects.service';
import { TeamProjectsQuery } from '../../stores/team-projects.query';
import { ICollectionPageEvent, IPagination } from '@core/types';
import { LeaseProjectSort, TeamProjectsQueryParams } from '../../types/teamwork-query-params.type';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';
import { LeaseProject } from '../../types/lease-project.type';

@Component({
    selector: 'avl-team-projects-page',
    templateUrl: './team-projects-page.component.html',
    styleUrls: ['./team-projects-page.component.scss'],
})
export class TeamProjectsPageComponent implements OnDestroy {
    public isLoading$ = this.teamProjectsQuery.selectLoading();
    public projects$ = this.teamProjectsQuery.selectAll();
    public pagination$: Observable<IPagination> = this.teamProjectsQuery.selectPagination();

    private lastPageEvent: ICollectionPageEvent | null = null;
    private lastSearchQuery: string | null = null;

    constructor(
        private readonly teamProjectsService: TeamProjectsService,
        private readonly teamProjectsQuery: TeamProjectsQuery,
        private readonly router: Router,
    ) {
    }

    public ngOnDestroy(): void {
        this.teamProjectsService.clear();
    }

    public onSearch(query: string): void {
        this.lastSearchQuery = query.length ? query.trim() : null;
        this.onPageChanged(this.lastPageEvent);
    }

    public onPageChanged(page: ICollectionPageEvent): void {
        const sortOptions = ['lastOpenedAt', 'projectName', 'matterNumber', 'owner', 'documentCount'];
        const normalizedSortOptions = page.sort === 'leases' ? 'documentCount' : page.sort;
        const sort = sortOptions.includes(normalizedSortOptions)
            ? normalizedSortOptions as LeaseProjectSort
            : undefined;
        const params: TeamProjectsQueryParams = {
            limit: page.pageSize,
            offset: page.pageIndex * page.pageSize,
            sort: sort,
            order: page.order,
            q: this.lastSearchQuery,
        };

        this.lastPageEvent = page;
        this.teamProjectsService.loadProjects(params);
    }

    public onProjectOpened(item: LeaseProject): void {
        void this.router.navigate(['lease/upload'], { queryParams: { fid: item.id } });
    }

    public onReportDownloaded(path: string): void {
        this.teamProjectsService.downloadReport(path);
    }
}
