import { Injectable } from '@angular/core';
import { EntityState, EntityStore, StoreConfig } from '@datorama/akita';
import { LeaseProject } from '../types/lease-project.type';
import { PaginatedData } from '@core/types';

export type TeamProjectsState = EntityState<LeaseProject>
    & Omit<PaginatedData<LeaseProject>, 'items'>;

const defaultState: Omit<PaginatedData<LeaseProject>, 'items'> = {
    offset: 0,
    limit: 0,
    total: 0,
};

@Injectable()
@StoreConfig({ name: '[lease] team-projects', resettable: true })
export class TeamProjectsStore extends EntityStore<TeamProjectsState> {

    constructor() {
        super(defaultState);
    }

}
