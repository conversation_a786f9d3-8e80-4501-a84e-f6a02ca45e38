import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TeamProjectsQueryParams } from '../types/teamwork-query-params.type';
import { LeaseProject } from '../types/lease-project.type';
import { PaginatedData } from '@core/types';
import { INotificationCounter } from '../../../../titles/types';

@Injectable()
export class TeamworkApi {

    constructor(private readonly http: HttpClient) {
    }

    public getProjects(queryParams?: TeamProjectsQueryParams): Observable<HttpResponse<PaginatedData<LeaseProject>>> {
        const url = '/api/filed-documents/teamwork';
        let params = new HttpParams();

        if (queryParams?.q) {
            params = params.set('q', queryParams.q);
        }
        if (queryParams?.limit !== undefined) {
            params = params.set('limit', queryParams.limit.toString());
        }
        if (queryParams?.offset !== undefined) {
            params = params.set('start', queryParams.offset.toString());
        }
        if (queryParams?.sort) {
            params = params.set('sort', queryParams.sort);
        }
        if (queryParams?.order) {
            const order = queryParams.order === 'asc' ? 'asc' : 'dsc';
            params = params.set('order', order);
        }

        return this.http.get<PaginatedData<LeaseProject>>(url, { params, observe: 'response' });
    }

    public getActiveProjects(): Observable<INotificationCounter> {
        return this.http.get<INotificationCounter>('/api/filed-documents/teamwork/active');
    }
}
