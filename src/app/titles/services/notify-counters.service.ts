import { Injectable } from '@angular/core';
import { BookmarksApi } from '../api';
import { forkJoin, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { NotifyCountersStore } from '../store';
import { INotificationCounter } from '../types';
import { PurchaseService } from './purchase.service';
import { TeamworkApi } from '../modules/teamwork/api/teamwork.api';

@Injectable()
export class NotifyCountersService {

    constructor(
        private readonly bookmarksApi: BookmarksApi,
        private readonly purchaseService: PurchaseService,
        private readonly teamworkApi: TeamworkApi,
        private readonly notifyCountersStore: NotifyCountersStore,
    ) {
    }

    public getCounters(): Observable<[INotificationCounter, INotificationCounter, INotificationCounter]> {
        return forkJoin([
            this.purchaseService.getActivePurchases(),
            this.bookmarksApi.getActiveBookmarks(),
            this.teamworkApi.getActiveProjects(),
        ])
            .pipe(
                tap((response) => {
                    this.notifyCountersStore.update({
                        purchased: response[0],
                        bookmarks: response[1],
                        teamProjects: response[2],
                    });
                }),
            );
    }
}
