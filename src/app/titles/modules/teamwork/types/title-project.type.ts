import { ScheduleInfo } from './schedule-info.type';
import { ProjectState } from '../enums/project-state.enum';
import { ProjectRegion } from '../enums/project-region.enum';

export type TitleProject = {
    id: string;
    projectName: string;
    matterNumber: string;
    owner: string;
    lastOpenedAt: string;
    officialCopiesUri?: string;
    titleNumbers: string[];
    schedules: ScheduleInfo[];
    documentCount: number;
    region: ProjectRegion;
    state: ProjectState;
}
