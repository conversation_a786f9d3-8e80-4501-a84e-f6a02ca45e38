import { Injectable } from '@angular/core';
import { TeamworkApi } from '../api/teamwork.api';
import { TeamProjectsQueryParams } from '../types/teamwork-query-params.type';
import { LoggerService } from '@services';
import { TeamProjectsStore } from './team-projects.store';
import { finalize } from 'rxjs/operators';

@Injectable()
export class TeamProjectsService {

    constructor(
        private readonly api: TeamworkApi,
        private readonly store: TeamProjectsStore,
        private readonly log: LoggerService,
    ) {
    }

    public loadProjects(queryParams?: TeamProjectsQueryParams): void {
        this.store.setLoading(true);
        this.api.getProjects(queryParams)
            .pipe(finalize(() => this.store.setLoading(false)))
            .subscribe({
                next: (response) => {
                    const items = response.body.items;
                    const paginatedData = {
                        offset: response.body.offset,
                        limit: response.body.limit,
                        total: response.body.total,
                    };

                    this.store.set(items);
                    this.store.update(paginatedData);
                },
                error: (error) => {
                    this.log.error('Team projects loading error', error);

                    if (error.status === 404) {
                        this.store.set([]);
                        return;
                    }

                    this.store.setError('Failed to load team projects');
                },
            });
    }

    public clear(): void {
        this.store.reset();
    }
}
