import { Injectable } from '@angular/core';
import { EntityState, EntityStore, StoreConfig } from '@datorama/akita';
import { TitleProject } from '../types/title-project.type';
import { PaginatedData } from '@core/types';

export type TeamProjectsState = EntityState<TitleProject>
    & Omit<PaginatedData<TitleProject>, 'items'>;

const defaultState: Omit<PaginatedData<TitleProject>, 'items'> = {
    offset: 0,
    limit: 0,
    total: 0,
};

@Injectable()
@StoreConfig({ name: 'team-projects', resettable: true })
export class TeamProjectsStore extends EntityStore<TeamProjectsState> {

    constructor() {
        super(defaultState);
    }

}
