import { Component, OnDestroy } from '@angular/core';
import { TeamProjectsService } from '../../stores/team-projects.service';
import { TeamProjectsQuery } from '../../stores/team-projects.query';
import { ICollectionPageEvent, IPagination } from '@core/types';
import { TeamProjectsQueryParams, TitleProjectSort } from '../../types/teamwork-query-params.type';
import { Observable } from 'rxjs';
import { TitleProject } from '../../types/title-project.type';
import { RefreshService, ReportService } from '../../../../services';
import { Router } from '@angular/router';

@Component({
    selector: 'avl-team-projects-page',
    templateUrl: './team-projects-page.component.html',
    styleUrls: ['./team-projects-page.component.scss'],
})
export class TeamProjectsPageComponent implements OnDestroy {
    public isLoading$ = this.teamProjectsQuery.selectLoading();
    public projects$ = this.teamProjectsQuery.selectAll();
    public pagination$: Observable<IPagination> = this.teamProjectsQuery.selectPagination();

    private lastPageEvent: ICollectionPageEvent | null = null;
    private lastSearchQuery: string | null = null;

    constructor(
        private readonly teamProjectsService: TeamProjectsService,
        private readonly teamProjectsQuery: TeamProjectsQuery,
        private readonly refreshService: RefreshService,
        private readonly router: Router,
        private readonly reportService: ReportService,
    ) {
    }

    public ngOnDestroy(): void {
        this.teamProjectsService.clear();
    }

    public onSearch(query: string): void {
        this.lastSearchQuery = query.length ? query.trim() : null;
        this.onPageChanged(this.lastPageEvent);
    }

    public onPageChanged(page: ICollectionPageEvent): void {
        const sortOptions = ['lastOpenedAt', 'projectName', 'matterNumber', 'owner', 'documentCount'];
        const normalizedSortOptions = page.sort === 'titles' ? 'documentCount' : page.sort;
        const sort = sortOptions.includes(normalizedSortOptions)
            ? normalizedSortOptions as TitleProjectSort
            : undefined;
        const params: TeamProjectsQueryParams = {
            limit: page.pageSize,
            offset: page.pageIndex * page.pageSize,
            sort: sort,
            order: page.order,
            q: this.lastSearchQuery,
        };

        this.lastPageEvent = page;
        this.teamProjectsService.loadProjects(params);
    }

    public onProjectOpened(item: TitleProject): void {
        this.router.navigate(['title/upload'], { queryParams: { fid: item.id } });
        this.refreshService.tryRefresh(item.lastOpenedAt);
    }

    public onReportDownloaded(path: string): void {
        this.reportService.downloadReport(path);
    }
}
