import { Component, Input } from '@angular/core';
import { TitleProject } from '../../types/title-project.type';

@Component({
    selector: 'avl-team-project-details',
    templateUrl: './team-project-details.component.html',
    styleUrls: ['./team-project-details.component.scss'],
})
export class TeamProjectDetailsComponent {
    @Input()
    public project: TitleProject;

    public isSchedules(): boolean {
        return !!this.project.schedules?.length;
    }

    public isTitleNumbers(): boolean {
        return !!this.project.titleNumbers?.length;
    }
}
