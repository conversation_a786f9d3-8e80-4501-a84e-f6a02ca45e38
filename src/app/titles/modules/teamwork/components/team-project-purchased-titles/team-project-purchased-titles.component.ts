import {
    AfterViewInit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
    QueryList,
    ViewChildren,
    ChangeDetectorRef,
    OnInit,
} from '@angular/core';

@Component({
    selector: 'avl-team-project-purchased-titles',
    templateUrl: './team-project-purchased-titles.component.html',
    styleUrls: ['./team-project-purchased-titles.component.scss'],
})
export class TeamProjectPurchasedTitlesComponent implements OnInit, AfterViewInit, OnDestroy {
    @Input()
    public titleNumbers: string[];

    @Output()
    public searched = new EventEmitter<string>();

    @ViewChildren('itemRef')
    public itemElements: QueryList<ElementRef<HTMLDivElement>>;

    public isExpanded = false;
    public isMoreThenOneRow = false;
    public searchQuery = '';
    public sortedItems: { title: string; isMatched: boolean }[] = [];

    private resizeObserver: ResizeObserver;
    private visibleItemsCount = 0;

    constructor(
        private readonly changeDetectorRef: ChangeDetectorRef,
    ) {
    }

    public ngOnInit(): void {
        this.updateSortedTitles();
    }

    public onSearch(query: string): void {
        this.searched.emit(query);
    }

    public onInputValueChanged(query: string): void {
        this.searchQuery = query;
        this.updateSortedTitles();
    }

    public toggleExpand(): void {
        this.isExpanded = !this.isExpanded;
    }

    public ngAfterViewInit(): void {
        this.countVisible();
        this.resizeObserver = new ResizeObserver(() => this.countVisible());

        if (this.itemElements.length) {
            this.resizeObserver.observe(this.itemElements.first.nativeElement.parentElement);
        }
    }

    public ngOnDestroy(): void {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
    }

    private countVisible(): void {
        if (!this.itemElements.length) {
            return;
        }

        const firstRowTop = this.itemElements.first.nativeElement.offsetTop;
        this.visibleItemsCount = this.itemElements
            .filter((el) => el.nativeElement.offsetTop === firstRowTop)
            .length;
        this.isMoreThenOneRow = this.visibleItemsCount < this.titleNumbers.length;
        this.changeDetectorRef.detectChanges();
    }

    private updateSortedTitles(): void {
        if (!this.searchQuery) {
            this.sortedItems = this.titleNumbers.map((title) => ({
                title,
                isMatched: false,
            }));
            return;
        }

        const query = this.searchQuery.toLowerCase();
        this.sortedItems = this.titleNumbers
            .map((title) => ({
                title,
                isMatched: !!title?.toLowerCase().includes(query),
            }))
            .sort((a, b) => {
                if (a.isMatched === b.isMatched) {
                    return 0;
                }

                return a.isMatched ? -1 : 1;
            });
    }
}
