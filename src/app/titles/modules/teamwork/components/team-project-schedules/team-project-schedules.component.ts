import { Component, Input } from '@angular/core';
import { ScheduleInfo } from '../../types/schedule-info.type';

@Component({
    selector: 'avl-team-project-schedules',
    templateUrl: './team-project-schedules.component.html',
    styleUrls: ['./team-project-schedules.component.scss'],
})
export class TeamProjectSchedulesComponent {
    @Input()
    public schedules: ScheduleInfo[] = [];

    public rowTrack(index: number, item: ScheduleInfo): string {
        return item;
    }
}
