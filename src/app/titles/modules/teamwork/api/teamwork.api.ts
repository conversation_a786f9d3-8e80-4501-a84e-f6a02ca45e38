import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { TeamProjectsQueryParams } from '../types/teamwork-query-params.type';
import { TitleProject } from '../types/title-project.type';
import { INotificationCounter } from '../../../types';
import { PaginatedData } from '@core/types';
import { catchError } from 'rxjs/operators';
import { LoggerService } from '@services';

@Injectable()
export class TeamworkApi {

    constructor(
        private readonly http: HttpClient,
        private readonly log: LoggerService,
    ) {
    }

    public getProjects(queryParams?: TeamProjectsQueryParams): Observable<HttpResponse<PaginatedData<TitleProject>>> {
        const url = '/api/titles/teamwork';
        let params = new HttpParams();

        if (queryParams?.q) {
            params = params.set('q', queryParams.q);
        }
        if (queryParams?.limit !== undefined) {
            params = params.set('limit', queryParams.limit.toString());
        }
        if (queryParams?.offset !== undefined) {
            params = params.set('start', queryParams.offset.toString());
        }
        if (queryParams?.sort) {
            params = params.set('sort', queryParams.sort);
        }
        if (queryParams?.order) {
            const order = queryParams.order === 'asc' ? 'asc' : 'dsc';
            params = params.set('order', order);
        }

        return this.http.get<PaginatedData<TitleProject>>(url, { params, observe: 'response' });
    }

    public getActiveProjects(): Observable<INotificationCounter> {
        return this.http.get<INotificationCounter>('/api/titles/teamwork/active')
            .pipe(
                catchError((error) => {
                    this.log.error('Fetching teamwork active projects counter failed', error);

                    return of(null);
                }),
            );
    }
}
