import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TeamworkApi } from './api/teamwork.api';
import { TeamProjectsService } from './stores/team-projects.service';
import { TeamProjectsStore } from './stores/team-projects.store';
import { TeamProjectsPageComponent } from './components/team-projects-page/team-projects-page.component';
import { TeamProjectDetailsComponent } from './components/team-project-details/team-project-details.component';
import { TeamProjectsTableComponent } from './components/team-projects-table/team-projects-table.component';
import { TeamProjectSchedulesComponent } from './components/team-project-schedules/team-project-schedules.component';
import { TeamProjectPurchasedTitlesComponent } from './components/team-project-purchased-titles/team-project-purchased-titles.component';
import { SharedModule } from '@shared/shared.module';
import { TeamProjectsQuery } from './stores/team-projects.query';
import { MatMenuModule } from '@angular/material/menu';

@NgModule({
    declarations: [
        TeamProjectsPageComponent,
        TeamProjectDetailsComponent,
        TeamProjectsTableComponent,
        TeamProjectSchedulesComponent,
        TeamProjectPurchasedTitlesComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        MatMenuModule,
    ],
    providers: [
        TeamworkApi,
        TeamProjectsService,
        TeamProjectsStore,
        TeamProjectsQuery,
    ],
})
export class TeamworkModule {
}
