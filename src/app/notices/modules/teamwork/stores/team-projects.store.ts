import { Injectable } from '@angular/core';
import { EntityState, EntityStore, StoreConfig } from '@datorama/akita';
import { PaginatedData } from '@core/types';
import { NoticeProject } from '../types/notice-project.type';

export type TeamProjectsState = EntityState<NoticeProject>
    & Omit<PaginatedData<NoticeProject>, 'items'>;

const defaultState: Omit<PaginatedData<NoticeProject>, 'items'> = {
    offset: 0,
    limit: 0,
    total: 0,
};

@Injectable()
@StoreConfig({ name: '[notice] team-projects', resettable: true, idKey: 'projectName' })
export class TeamProjectsStore extends EntityStore<TeamProjectsState> {

    constructor() {
        super(defaultState);
    }

}
